import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { createSeriesApi } from '../services/api';
import { useApiClient } from '../services/apiClient';
import type {
  Series,
  CreateSeriesRequest,
  UpdateSeriesRequest,
  SeriesFilters
} from '../types/series';

// Query keys
export const seriesKeys = {
  all: ['series'] as const,
  lists: () => [...seriesKeys.all, 'list'] as const,
  list: (filters: SeriesFilters) => {
    // Create a stable, serializable key
    const stableFilters = {
      page: filters.page || 1,
      pageSize: filters.pageSize || 20,
      search: filters.search || '',
      status: filters.status || '',
      sortBy: filters.sortBy || 'name',
      sortOrder: filters.sortOrder || 'asc',
      excludePlanToRead: filters.excludePlanToRead || false,
    };
    return [...seriesKeys.lists(), stableFilters] as const;
  },
  details: () => [...seriesKeys.all, 'detail'] as const,
  detail: (id: string) => [...seriesKeys.details(), id] as const,
  statuses: () => [...seriesKeys.all, 'statuses'] as const,
};

// Get series list with filters
export function useSeries(filters: SeriesFilters = {}) {
  const { apiCall } = useApiClient();
  const seriesApi = createSeriesApi(apiCall);

  // Use the stable query key from seriesKeys
  const queryKey = seriesKeys.list(filters);

  return useQuery({
    queryKey,
    queryFn: async () => {
      const result = await seriesApi.getSeries(filters);

      // If backend doesn't support excludePlanToRead, filter on frontend
      if (filters.excludePlanToRead && result.data) {
        const filteredData = result.data.filter(series => series.status !== 'Plan to Read');
        return {
          ...result,
          data: filteredData,
          totalCount: filteredData.length,
          totalPages: Math.ceil(filteredData.length / (filters.pageSize || 20))
        };
      }

      return result;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchInterval: false,
    placeholderData: (previousData) => previousData, // Keep previous data while loading new page
  });
}

// Get single series by ID
export function useSeriesById(id: string) {
  const { apiCall } = useApiClient();
  const seriesApi = createSeriesApi(apiCall);

  return useQuery({
    queryKey: seriesKeys.detail(id),
    queryFn: () => seriesApi.getSeriesById(id),
    enabled: !!id,
    staleTime: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: true,
  });
}

// Get available statuses
export function useSeriesStatuses() {
  const { apiCall } = useApiClient();
  const seriesApi = createSeriesApi(apiCall);

  return useQuery({
    queryKey: seriesKeys.statuses(),
    queryFn: () => seriesApi.getStatuses(),
    staleTime: 60 * 60 * 1000, // 1 hour (statuses rarely change)
  });
}

// Create series mutation
export function useCreateSeries() {
  const { apiCall } = useApiClient();
  const seriesApi = createSeriesApi(apiCall);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSeriesRequest) => seriesApi.createSeries(data),
    onSuccess: () => {
      // Invalidate all series list queries
      queryClient.invalidateQueries({
        queryKey: seriesKeys.lists(),
        exact: false
      });
    },
  });
}

// Update series mutation
export function useUpdateSeries() {
  const { apiCall } = useApiClient();
  const seriesApi = createSeriesApi(apiCall);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateSeriesRequest }) =>
      seriesApi.updateSeries(id, data),
    onSuccess: (updatedSeries) => {
      // Update the specific series in cache
      queryClient.setQueryData(
        seriesKeys.detail(updatedSeries.id),
        updatedSeries
      );

      // Invalidate series lists to reflect changes
      queryClient.invalidateQueries({
        queryKey: seriesKeys.lists(),
        exact: false
      });
    },
  });
}

// Delete series mutation
export function useDeleteSeries() {
  const { apiCall } = useApiClient();
  const seriesApi = createSeriesApi(apiCall);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => seriesApi.deleteSeries(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: seriesKeys.detail(deletedId) });

      // Invalidate series lists
      queryClient.invalidateQueries({
        queryKey: seriesKeys.lists(),
        exact: false
      });
    },
  });
}

// Optimistic update for quick status changes
export function useQuickUpdateSeries() {
  const { apiCall } = useApiClient();
  const seriesApi = createSeriesApi(apiCall);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateSeriesRequest }) =>
      seriesApi.updateSeries(id, data),
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: seriesKeys.detail(id) });

      // Snapshot previous value
      const previousSeries = queryClient.getQueryData<Series>(seriesKeys.detail(id));

      // Optimistically update
      if (previousSeries) {
        queryClient.setQueryData<Series>(seriesKeys.detail(id), {
          ...previousSeries,
          ...data,
          updatedAt: new Date().toISOString(),
        });
      }

      return { previousSeries };
    },
    onError: (_, { id }, context) => {
      // Rollback on error
      if (context?.previousSeries) {
        queryClient.setQueryData(seriesKeys.detail(id), context.previousSeries);
      }
    },
    onSettled: (_, __, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: seriesKeys.detail(id) });
      queryClient.invalidateQueries({
        queryKey: seriesKeys.lists(),
        exact: false
      });
    },
  });
}
