import { useAuth0 } from '@auth0/auth0-react';
import { useCallback } from 'react';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://127.0.0.1:8787';

export class ApiError extends Error {
  status: number;
  details?: any;

  constructor(status: number, message: string, details?: any) {
    super(message);
    this.status = status;
    this.name = 'ApiError';
    this.details = details;
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string = 'Authentication required') {
    super(401, message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends ApiError {
  constructor(message: string = 'Insufficient permissions') {
    super(403, message);
    this.name = 'AuthorizationError';
  }
}

export class DuplicateSeriesError extends Error {
  conflictData: any;

  constructor(conflictData: any) {
    super('A series with this name already exists');
    this.name = 'DuplicateSeriesError';
    this.conflictData = conflictData;
  }
}

export function useApiClient() {
  const { getAccessTokenSilently, loginWithRedirect } = useAuth0();

  const apiCall = useCallback(async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> => {
    try {
      const token = await getAccessTokenSilently();
      
      const url = `${API_BASE_URL}${endpoint}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          ...options.headers,
        },
      });

      // Handle different response types
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        switch (response.status) {
          case 401:
            // Token expired or invalid, try to get a new one
            try {
              await getAccessTokenSilently({ cacheMode: 'off' });
              // If successful, the user can retry the request
              throw new AuthenticationError(errorData.message || 'Authentication failed');
            } catch {
              // If refresh fails, redirect to login
              loginWithRedirect();
              throw new AuthenticationError('Please log in again');
            }
          
          case 403:
            throw new AuthorizationError(
              errorData.message || 'You do not have permission to perform this action'
            );
          
          case 409:
            // Handle duplicate series conflict
            if (errorData.existingSeries && errorData.proposedData) {
              throw new DuplicateSeriesError(errorData);
            }
            throw new ApiError(409, errorData.message || 'Conflict occurred', errorData);
          
          case 404:
            throw new ApiError(404, errorData.message || 'Resource not found');
          
          case 400:
            throw new ApiError(400, errorData.message || 'Invalid request', errorData.details);
          
          default:
            throw new ApiError(
              response.status,
              errorData.message || `HTTP ${response.status}`,
              errorData
            );
        }
      }

      // Handle 204 No Content responses
      if (response.status === 204) {
        return null as T;
      }

      return await response.json();
    } catch (error) {
      // Re-throw our custom errors without modification
      if (
        error instanceof ApiError ||
        error instanceof AuthenticationError ||
        error instanceof AuthorizationError ||
        error instanceof DuplicateSeriesError
      ) {
        throw error;
      }

      // Handle network errors
      throw new ApiError(
        0,
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }, [getAccessTokenSilently, loginWithRedirect]);

  return { apiCall };
}
