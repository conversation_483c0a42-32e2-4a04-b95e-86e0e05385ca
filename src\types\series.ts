export interface Series {
  id: string;
  name: string;
  chapter: number | null;
  status: string;
  updatedAt: string;
  user_id: string;
}

export interface PaginatedResult<T> {
  data: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface CreateSeriesRequest {
  name: string;
  chapter?: number;
  status: string;
}

export interface UpdateSeriesRequest {
  name?: string;
  chapter?: number;
  status?: string;
}

export interface SeriesFilters {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  excludeNullChapter?: boolean;
  excludePlanToRead?: boolean;
}

export const SERIES_STATUSES = [
  "Reading",
  "Completed",
  "On-Hold",
  "Dropped",
  "Cancelled",
  "Plan to Read",
] as const;

export type SeriesStatus = (typeof SERIES_STATUSES)[number];

export const STATUS_COLORS: Record<SeriesStatus, string> = {
  Reading: "bg-[#38b2ac] text-[#faf6ee] border-[#38b2ac]",
  Completed: "bg-[#83af51] text-[#faf6ee] border-[#83af51]",
  "On-Hold": "bg-[#b28b38] text-[#faf6ee] border-[#b28b38]",
  Dropped: "bg-[#b23838] text-[#faf6ee] border-[#b23838]",
  Cancelled: "bg-[#3c3c3c] text-[#faf6ee] border-[#3c3c3c]",
  "Plan to Read": "bg-[#96b38a] text-[#faf6ee] border-[#96b38a]",
};

export interface DuplicateSeriesResponse {
  error: string;
  message: string;
  existingSeries: Series;
  proposedData: CreateSeriesRequest;
}

export interface ConflictResolutionOptions {
  updateExisting: boolean;
  modifyName: boolean;
  cancel: boolean;
}
