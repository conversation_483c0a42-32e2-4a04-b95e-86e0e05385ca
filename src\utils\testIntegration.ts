// Test utility to verify the integration is working correctly
export const testIntegration = {
  // Test environment variables
  checkEnvironment: () => {
    const required = [
      'VITE_AUTH0_DOMAIN',
      'VITE_AUTH0_CLIENT_ID', 
      'VITE_AUTH0_AUDIENCE',
      'VITE_API_URL'
    ];

    const missing = required.filter(key => !import.meta.env[key]);
    
    if (missing.length > 0) {
      console.error('❌ Missing environment variables:', missing);
      return false;
    }
    
    console.log('✅ All environment variables are set');
    return true;
  },

  // Test API connectivity (without auth)
  checkApiConnectivity: async () => {
    const apiUrl = import.meta.env.VITE_API_URL;
    
    try {
      const response = await fetch(`${apiUrl}/api/series/statuses`);
      
      if (response.status === 401) {
        console.log('✅ API is reachable (authentication required as expected)');
        return true;
      }
      
      if (response.ok) {
        console.log('✅ API is reachable and responding');
        return true;
      }
      
      console.error('❌ API returned error:', response.status);
      return false;
    } catch (error) {
      console.error('❌ Cannot reach API:', error);
      return false;
    }
  },

  // Run all tests
  runAll: async () => {
    console.log('🧪 Running integration tests...\n');
    
    const envTest = testIntegration.checkEnvironment();
    const apiTest = await testIntegration.checkApiConnectivity();
    
    console.log('\n📊 Test Results:');
    console.log(`Environment: ${envTest ? '✅' : '❌'}`);
    console.log(`API Connectivity: ${apiTest ? '✅' : '❌'}`);
    
    if (envTest && apiTest) {
      console.log('\n🎉 Integration tests passed! You can proceed with authentication.');
    } else {
      console.log('\n⚠️  Some tests failed. Please check your configuration.');
    }
    
    return envTest && apiTest;
  }
};

// Auto-run tests in development
if (import.meta.env.DEV) {
  // Run tests after a short delay to ensure environment is loaded
  setTimeout(() => {
    testIntegration.runAll();
  }, 1000);
}
