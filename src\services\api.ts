import type {
  Series,
  PaginatedResult,
  CreateSeriesRequest,
  UpdateSeriesRequest,
  SeriesFilters,
} from "../types/series";
import {
  ApiError,
  DuplicateSeriesError,
  AuthenticationError,
  AuthorizationError,
} from "./apiClient";

// Re-export for backward compatibility
export {
  ApiError,
  DuplicateSeriesError,
  AuthenticationError,
  AuthorizationError,
};

// This function is now replaced by the useApiClient hook
// Keeping it for reference but it should not be used directly

// Factory function to create API methods with authenticated client
export const createSeriesApi = (
  apiCall: <T>(endpoint: string, options?: RequestInit) => Promise<T>
) => ({
  // Get paginated series list
  getSeries: async (
    filters: SeriesFilters = {}
  ): Promise<PaginatedResult<Series>> => {
    const params = new URLSearchParams();

    if (filters.page) params.append("page", filters.page.toString());
    if (filters.pageSize)
      params.append("pageSize", filters.pageSize.toString());
    if (filters.search) params.append("search", filters.search);
    if (filters.status) params.append("status", filters.status);
    if (filters.sortBy) params.append("sortBy", filters.sortBy);
    if (filters.sortOrder) params.append("sortOrder", filters.sortOrder);
    if (filters.excludeNullChapter) params.append("excludeNullChapter", "true");

    const queryString = params.toString();
    const endpoint = `/api/series${queryString ? `?${queryString}` : ""}`;

    const response = await apiCall<{
      success: boolean;
      data: PaginatedResult<Series>;
    }>(endpoint);

    // Extract the actual paginated result from the nested data structure
    if (response.success && response.data) {
      return response.data;
    }

    throw new ApiError(500, "Invalid API response format");
  },

  // Get specific series by ID
  getSeriesById: async (id: string): Promise<Series> => {
    return apiCall<Series>(`/api/series/${id}`);
  },

  // Create a new series
  createSeries: async (data: CreateSeriesRequest): Promise<Series> => {
    try {
      return await apiCall<Series>("/api/series", {
        method: "POST",
        body: JSON.stringify(data),
      });
    } catch (error) {
      // Re-throw DuplicateSeriesError with enhanced data if needed
      if (error instanceof DuplicateSeriesError) {
        // Ensure proposedData is available, use the original request data as fallback
        if (!error.conflictData.proposedData) {
          error.conflictData.proposedData = data;
        }
        throw error;
      }

      throw error;
    }
  },

  // Update series
  updateSeries: async (
    id: string,
    data: UpdateSeriesRequest
  ): Promise<Series> => {
    return apiCall<Series>(`/api/series/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  },

  // Delete series
  deleteSeries: async (id: string): Promise<void> => {
    return apiCall<void>(`/api/series/${id}`, {
      method: "DELETE",
    });
  },

  // Get available statuses
  getStatuses: async (): Promise<string[]> => {
    return apiCall<string[]>("/api/series/statuses");
  },
});

// Legacy export - use createSeriesApi with useApiClient instead
export const seriesApi = {
  getSeries: () => {
    throw new Error("Use createSeriesApi with useApiClient hook instead");
  },
  getSeriesById: () => {
    throw new Error("Use createSeriesApi with useApiClient hook instead");
  },
  createSeries: () => {
    throw new Error("Use createSeriesApi with useApiClient hook instead");
  },
  updateSeries: () => {
    throw new Error("Use createSeriesApi with useApiClient hook instead");
  },
  deleteSeries: () => {
    throw new Error("Use createSeriesApi with useApiClient hook instead");
  },
  getStatuses: () => {
    throw new Error("Use createSeriesApi with useApiClient hook instead");
  },
};
