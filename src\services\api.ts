import type {
  Series,
  PaginatedResult,
  CreateSeriesRequest,
  UpdateSeriesRequest,
  SeriesFilters,
  DuplicateSeriesResponse,
} from "../types/series";

const API_BASE_URL = "http://localhost:5174/api";

class ApiError extends <PERSON>rror {
  status: number;

  constructor(status: number, message: string) {
    super(message);
    this.status = status;
    this.name = "ApiError";
  }
}

export class DuplicateSeriesError extends Error {
  conflictData: DuplicateSeriesResponse;

  constructor(conflictData: DuplicateSeriesResponse) {
    super("A series with this name already exists");
    this.name = "DuplicateSeriesError";
    this.conflictData = conflictData;
  }
}

async function fetchApi<T>(
  endpoint: string,
  options?: RequestInit
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  try {
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      // Handle 409 Conflict for duplicate series
      if (response.status === 409) {
        const errorData = await response.json();
        console.log("Received conflict data:", errorData);

        // Try to handle different possible response structures
        let conflictData: DuplicateSeriesResponse | null = null;

        if (errorData && errorData.existingSeries && errorData.proposedData) {
          // Expected structure
          conflictData = errorData;
        } else if (errorData && errorData.existing && errorData.proposed) {
          // Alternative structure
          conflictData = {
            error: errorData.error || "Duplicate series",
            message: errorData.message || "A series with this name already exists",
            existingSeries: errorData.existing,
            proposedData: errorData.proposed
          };
        } else if (errorData && errorData.existingSeries) {
          // Only existing series provided, try to reconstruct proposed data
          conflictData = {
            error: errorData.error || "Duplicate series",
            message: errorData.message || "A series with this name already exists",
            existingSeries: errorData.existingSeries,
            proposedData: {
              name: errorData.existingSeries.name,
              status: "Plan to Read"
            }
          };
        } else if (errorData && errorData.series) {
          // Another possible structure where existing series is under 'series'
          conflictData = {
            error: errorData.error || "Duplicate series",
            message: errorData.message || "A series with this name already exists",
            existingSeries: errorData.series,
            proposedData: {
              name: errorData.series.name,
              status: "Plan to Read"
            }
          };
        } else if (errorData && (errorData.name || errorData.message)) {
          // Minimal structure - try to extract what we can
          const seriesName = errorData.name || (errorData.message && errorData.message.includes('"') ?
            errorData.message.split('"')[1] : "Unknown Series");

          conflictData = {
            error: errorData.error || "Duplicate series",
            message: errorData.message || "A series with this name already exists",
            existingSeries: {
              id: "unknown",
              name: seriesName,
              chapter: null,
              status: "Unknown",
              updatedAt: new Date().toISOString()
            },
            proposedData: {
              name: seriesName,
              status: "Plan to Read"
            }
          };
        }

        if (conflictData) {
          throw new DuplicateSeriesError(conflictData);
        } else {
          // Log the actual structure to help debug
          console.error("Unexpected 409 response structure:", errorData);
          throw new ApiError(
            response.status,
            `Duplicate series detected but response format is unexpected: ${JSON.stringify(errorData)}`
          );
        }
      }

      const errorText = await response.text();
      throw new ApiError(
        response.status,
        errorText || `HTTP ${response.status}`
      );
    }

    // Handle 204 No Content responses
    if (response.status === 204) {
      return null as T;
    }

    return await response.json();
  } catch (error) {
    // Re-throw our custom errors without modification
    if (error instanceof DuplicateSeriesError || error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(
      0,
      `Network error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

export const seriesApi = {
  // Get paginated series list
  getSeries: async (
    filters: SeriesFilters = {}
  ): Promise<PaginatedResult<Series>> => {
    const params = new URLSearchParams();

    if (filters.page) params.append("page", filters.page.toString());
    if (filters.pageSize)
      params.append("pageSize", filters.pageSize.toString());
    if (filters.search) params.append("search", filters.search);
    if (filters.status) params.append("status", filters.status);
    if (filters.sortBy) params.append("sortBy", filters.sortBy);
    if (filters.sortOrder) params.append("sortOrder", filters.sortOrder);
    if (filters.excludeNullChapter) params.append("excludeNullChapter", "true");

    const queryString = params.toString();
    const endpoint = `/series${queryString ? `?${queryString}` : ""}`;

    console.log("Fetching series with URL:", `${API_BASE_URL}${endpoint}`);
    
    try {
      const response = await fetchApi<{success: boolean, data: PaginatedResult<Series>}>(endpoint);
      console.log("API response:", response);
      
      // Extract the actual paginated result from the nested data structure
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new ApiError(500, "Invalid API response format");
    } catch (error) {
      console.error("Error fetching series:", error);
      throw error;
    }
  },

  // Get specific series by ID
  getSeriesById: async (id: string): Promise<Series> => {
    return fetchApi<Series>(`/series/${id}`);
  },

  // Create a new series
  createSeries: async (data: CreateSeriesRequest): Promise<Series> => {
    try {
      return await fetchApi<Series>("/series", {
        method: "POST",
        body: JSON.stringify(data),
      });
    } catch (error) {
      console.error("Error creating series:", error);

      // Re-throw DuplicateSeriesError with enhanced data if needed
      if (error instanceof DuplicateSeriesError) {
        // Ensure proposedData is available, use the original request data as fallback
        if (!error.conflictData.proposedData) {
          error.conflictData.proposedData = data;
        }
        throw error;
      }

      throw error;
    }
  },

  // Update series
  updateSeries: async (
    id: string,
    data: UpdateSeriesRequest
  ): Promise<Series> => {
    return fetchApi<Series>(`/series/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  },

  // Delete series
  deleteSeries: async (id: string): Promise<void> => {
    return fetchApi<void>(`/series/${id}`, {
      method: "DELETE",
    });
  },

  // Get available statuses
  getStatuses: async (): Promise<string[]> => {
    return fetchApi<string[]>("/series/statuses");
  },
};

export { ApiError };
