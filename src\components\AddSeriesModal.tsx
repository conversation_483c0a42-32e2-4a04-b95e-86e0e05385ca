import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Modal } from "./ui/Modal";
import { Input } from "./ui/Input";
import { Select } from "./ui/Select";
import { Button } from "./ui/Button";
import { DuplicateConflictModal } from "./DuplicateConflictModal";
import type {
  CreateSeriesRequest,
  DuplicateSeriesResponse,
} from "../types/series";
import { SERIES_STATUSES } from "../types/series";
import { useCreateSeries, useUpdateSeries } from "../hooks/useSeries";
import { DuplicateSeriesError } from "../services/api";

const createSeriesSchema = z.object({
  name: z
    .string()
    .min(1, "Series name is required")
    .max(200, "Name is too long"),
  chapter: z
    .number()
    .min(0, "Chapter must be 0 or greater")
    .multipleOf(0.1, "Chapter can have at most 1 decimal place")
    .optional()
    .or(z.literal("")),
  status: z.string().min(1, "Status is required"),
});

type CreateSeriesForm = z.infer<typeof createSeriesSchema>;

interface AddSeriesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AddSeriesModal({ isOpen, onClose }: AddSeriesModalProps) {
  const [conflictData, setConflictData] =
    useState<DuplicateSeriesResponse | null>(null);
  const [isConflictModalOpen, setIsConflictModalOpen] = useState(false);

  const createSeries = useCreateSeries();
  const updateSeries = useUpdateSeries();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<CreateSeriesForm>({
    resolver: zodResolver(createSeriesSchema),
    defaultValues: {
      name: "",
      chapter: "",
      status: "Plan to Read",
    },
  });

  const statusOptions = SERIES_STATUSES.map((status) => ({
    value: status,
    label: status,
  }));

  const onSubmit = async (data: CreateSeriesForm) => {
    const payload: CreateSeriesRequest = {
      name: data.name.trim(),
      status: data.status,
    };

    // Only include chapter if it's a valid number
    if (data.chapter !== "" && !isNaN(Number(data.chapter))) {
      payload.chapter = Number(data.chapter);
    }

    createSeries.mutate(payload, {
      onSuccess: () => {
        // Reset form and close modal on success
        reset();
        onClose();
      },
      onError: (error) => {
        // Handle duplicate series conflict
        if (error instanceof DuplicateSeriesError) {
          console.log("DuplicateSeriesError caught:", error.conflictData);

          // Validate the conflict data before using it
          if (error.conflictData && error.conflictData.existingSeries && error.conflictData.proposedData) {
            setConflictData(error.conflictData);
            setIsConflictModalOpen(true);
            return;
          } else {
            console.error("Invalid conflict data structure:", error.conflictData);
            // Fall through to general error handling
          }
        }
      },
    });
  };

  const handleUpdateExisting = async (data: CreateSeriesRequest) => {
    if (!conflictData) return;

    try {
      await updateSeries.mutateAsync({
        id: conflictData.existingSeries.id,
        data: {
          name: data.name,
          chapter: data.chapter,
          status: data.status,
        },
      });

      // Success - close all modals and reset
      setIsConflictModalOpen(false);
      setConflictData(null);
      reset();
      onClose();
    } catch (error) {
      // Error handling is managed by React Query
    }
  };

  const handleRetryWithNewName = async (data: CreateSeriesRequest) => {
    try {
      await createSeries.mutateAsync(data);

      // Success - close all modals and reset
      setIsConflictModalOpen(false);
      setConflictData(null);
      reset();
      onClose();
    } catch (error) {
      // Error handling is managed by React Query
    }
  };

  const handleConflictModalClose = () => {
    setIsConflictModalOpen(false);
    setConflictData(null);
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        title="Add New Series"
        size="md"
      >
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <Input
            label="Series Name"
            placeholder="Enter series name..."
            error={errors.name?.message}
            {...register("name")}
          />

          <Input
            label="Current Chapter"
            type="number"
            min="0"
            step="0.1"
            placeholder="0"
            helperText="Supports decimals (e.g., 130.2, 150.7)"
            error={errors.chapter?.message}
            {...register("chapter", {
              setValueAs: (value) => (value === "" ? "" : Number(value)),
            })}
          />

          <Select
            label="Status"
            options={statusOptions}
            error={errors.status?.message}
            {...register("status")}
          />

          {createSeries.error &&
            !(createSeries.error instanceof DuplicateSeriesError) && (
              <div className="p-3 bg-red-50 border border-red-200 ">
                <p className="text-sm text-red-600">
                  {createSeries.error instanceof Error
                    ? createSeries.error.message
                    : "Failed to create series. Please try again."}
                </p>
              </div>
            )}

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              Add Series
            </Button>
          </div>
        </form>
      </Modal>

      <DuplicateConflictModal
        isOpen={isConflictModalOpen}
        onClose={handleConflictModalClose}
        conflictData={conflictData}
        onUpdateExisting={handleUpdateExisting}
        onRetryWithNewName={handleRetryWithNewName}
        loading={updateSeries.isPending || createSeries.isPending}
      />
    </>
  );
}

