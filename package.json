{"name": "novel-archives", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@hookform/resolvers": "^5.0.1", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.80.3", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.4.1", "tailwindcss": "^4.1.8", "zod": "^3.25.51"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}