import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BookOpen, Calendar, Edit3 } from "lucide-react";
import { Modal } from "./ui/Modal";
import { Button } from "./ui/Button";
import { Input } from "./ui/Input";
import { StatusBadge } from "./StatusBadge";
import type {
  DuplicateSeriesResponse,
  CreateSeriesRequest,
  SeriesStatus,
} from "../types/series";
import { cn } from "../utils/cn";

interface DuplicateConflictModalProps {
  isOpen: boolean;
  onClose: () => void;
  conflictData: DuplicateSeriesResponse | null;
  onUpdateExisting: (data: CreateSeriesRequest) => void;
  onRetryWithNewName: (data: CreateSeriesRequest) => void;
  loading?: boolean;
}

export function DuplicateConflictModal({
  isOpen,
  onClose,
  conflictData,
  onUpdateExisting,
  onRetryWithNewName,
  loading = false,
}: DuplicateConflictModalProps) {
  const [selectedAction, setSelectedAction] = useState<
    "update" | "rename" | null
  >(null);
  const [newName, setNewName] = useState("");

  if (!conflictData) return null;

  const { existingSeries, proposedData } = conflictData;

  // Defensive check to ensure we have the required data
  if (!existingSeries || !proposedData) {
    console.error("Invalid conflict data structure:", conflictData);
    return (
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title="Duplicate Series Detected"
        size="md"
      >
        <div className="space-y-4">
          <div className="flex items-start gap-3 p-4 bg-red-50 border border-red-200">
            <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="font-medium text-red-800">
                Error Processing Duplicate Series
              </h3>
              <p className="text-sm text-red-700 mt-1">
                A duplicate series was detected, but the response format is unexpected. Please try again.
              </p>
            </div>
          </div>
          <div className="flex justify-end">
            <Button onClick={onClose}>Close</Button>
          </div>
        </div>
      </Modal>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatChapter = (chapter: number | null) => {
    if (chapter === null) return "Not started";
    return chapter % 1 === 0 ? chapter.toString() : chapter.toString();
  };

  const handleUpdateExisting = () => {
    onUpdateExisting(proposedData);
  };

  const handleRetryWithNewName = () => {
    if (!newName.trim()) return;
    onRetryWithNewName({
      ...proposedData,
      name: newName.trim(),
    });
  };

  const handleClose = () => {
    setSelectedAction(null);
    setNewName("");
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Duplicate Series Detected"
      size="lg"
    >
      <div className="space-y-6">
        {/* Warning Header */}
        <div className="flex items-start gap-3 p-4 bg-yellow-50 border border-yellow-200">
          <AlertTriangle className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="font-medium text-yellow-800">
              Series "{existingSeries.name}" already exists
            </h3>
            <p className="text-sm text-yellow-700 mt-1">
              Choose how you'd like to handle this conflict:
            </p>
          </div>
        </div>

        {/* Comparison */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Existing Series */}
          <div className="border border-[#3c3c3c] p-4">
            <h4 className="font-medium text-[#3c3c3c] mb-3 flex items-center gap-2">
              Existing Series
            </h4>
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-[#3c3c3c]">Name</p>
                <p className="text-[#3c3c3c]">{existingSeries.name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-[#3c3c3c]">Chapter</p>
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-gray-400" />
                  <span>{formatChapter(existingSeries.chapter)}</span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-[#3c3c3c]">Status</p>
                <StatusBadge status={existingSeries.status as SeriesStatus} />
              </div>
              <div>
                <p className="text-sm font-medium text-[#3c3c3c]">
                  Last Updated
                </p>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-[#3c3c3c]" />
                  <span className="text-sm text-[#3c3c3c]">
                    {formatDate(existingSeries.updatedAt)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Proposed Series */}
          <div className="border border-[#3c3c3c]  p-4">
            <h4 className="font-medium text-[#3c3c3c] mb-3 flex items-center gap-2">
              Your New Data
            </h4>
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-[#3c3c3c]">Name</p>
                <p className="text-[#3c3c3c]">{proposedData.name}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-[#3c3c3c]">Chapter</p>
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-[#3c3c3c]" />
                  <span>{formatChapter(proposedData.chapter || null)}</span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-[#3c3c3c]">Status</p>
                <StatusBadge status={proposedData.status as SeriesStatus} />
              </div>
              <div>
                <p className="text-sm font-medium text-[#3c3c3c]">
                  Would be created
                </p>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-[#3c3c3c]" />
                  <span className="text-sm text-[#3c3c3c]">Now</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Selection */}
        <div className="space-y-4">
          <h4 className="font-medium text-[#3c3c3c]">Choose an action:</h4>

          {/* Update Existing Option */}
          <div
            className={cn(
              "border p-4 cursor-pointer transition-all",
              selectedAction === "update"
                ? ""
                : "border-[#3c3c3c] hover:bg-[#faf6ee]"
            )}
            onClick={() => setSelectedAction("update")}
          >
            <div className="flex items-start gap-3">
              <input
                type="radio"
                checked={selectedAction === "update"}
                onChange={() => setSelectedAction("update")}
                className="mt-1"
              />
              <div className="flex-1">
                <h5 className="font-medium text-gray-900">
                  Update Existing Series
                </h5>
                <p className="text-sm text-gray-600 mt-1">
                  Replace the existing series data with your new information
                </p>
                {(proposedData.chapter !== existingSeries.chapter ||
                  proposedData.status !== existingSeries.status) && (
                  <div className="mt-2 text-xs">
                    Will update:{" "}
                    {proposedData.chapter !== existingSeries.chapter &&
                      "Chapter"}
                    {proposedData.chapter !== existingSeries.chapter &&
                      proposedData.status !== existingSeries.status &&
                      ", "}
                    {proposedData.status !== existingSeries.status && "Status"}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Rename Option */}
          <div
            className={cn(
              "border p-4 cursor-pointer transition-all",
              selectedAction === "rename"
                ? ""
                : "border-[#3c3c3c] hover:bg-[#faf6ee]"
            )}
            onClick={() => setSelectedAction("rename")}
          >
            <div className="flex items-start gap-3">
              <input
                type="radio"
                checked={selectedAction === "rename"}
                onChange={() => setSelectedAction("rename")}
                className="mt-1"
              />
              <div className="flex-1">
                <h5 className="font-medium text-gray-900">
                  Change Name and Create New
                </h5>
                <p className="text-sm text-gray-600 mt-1">
                  Modify the series name and create it as a separate entry
                </p>
                {selectedAction === "rename" && (
                  <div className="mt-3">
                    <Input
                      placeholder="Enter new series name..."
                      value={newName}
                      onChange={(e) => setNewName(e.target.value)}
                      className="w-full"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>

          {selectedAction === "update" && (
            <Button
              onClick={handleUpdateExisting}
              loading={loading}
              disabled={loading}
              className="flex items-center gap-2"
            >
              <Edit3 className="h-4 w-4" />
              Update Existing
            </Button>
          )}

          {selectedAction === "rename" && (
            <Button
              onClick={handleRetryWithNewName}
              loading={loading}
              disabled={loading || !newName.trim()}
              className="flex items-center gap-2"
            >
              Create with New Name
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
}

