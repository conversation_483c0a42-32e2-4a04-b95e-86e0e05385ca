import { useAuth0 } from '@auth0/auth0-react';
import { LogIn } from 'lucide-react';

export function LoginButton() {
  const { loginWithRedirect, isLoading } = useAuth0();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Novel Archives
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Track your reading progress
          </p>
        </div>
        <div className="mt-8 space-y-6">
          <button
            onClick={() => loginWithRedirect()}
            disabled={isLoading}
            className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span className="absolute left-0 inset-y-0 flex items-center pl-3">
              <LogIn className="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" />
            </span>
            {isLoading ? 'Loading...' : 'Sign in to continue'}
          </button>
        </div>
      </div>
    </div>
  );
}
