import type { SeriesStatus } from "../types/series";
import { STATUS_COLORS } from "../types/series";
import { cn } from "../utils/cn";

interface StatusBadgeProps {
  status: SeriesStatus;
  className?: string;
  onClick?: () => void;
}

export function StatusBadge({ status, className, onClick }: StatusBadgeProps) {
  const colorClasses = STATUS_COLORS[status];

  return (
    <span
      className={cn(
        "inline-flex items-center px-2.5 py-0.5 text-xs font-medium border",
        "max-w-full truncate",
        colorClasses,
        onClick && "cursor-pointer hover:opacity-80 transition-opacity",
        className
      )}
      onClick={onClick}
    >
      {status}
    </span>
  );
}
